using App.Base.Repository.Entities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Repository.Interface;

public interface IItemsRepository
{
    //=== Items
    Items CreateItems(Items item);
    Task<List<Items>> CreateItemsAsync(IEnumerable<Items> items);
    Items RestoreItems(Items item);
    Items DeleteItems(string itemsId);
    Items? FindByItemsId(string itemsId);
    Items? FindByExternalId(string externalId, SyncServiceEnum externalSource);
    Items? FindByItemsName(string itemsName);

    List<Items> FindByExternalIds(string externalId, SyncServiceEnum externalSource);

    List<Items> FindByItemsCode(string itemsCode);

    List<Items> FindByItemsIds(string itemsIds);
    List<Items> FindByItemsCodes(List<string> itemsCodes);
    PagingResult<Items> ListItems(Paging paging, string? partnerId = null, string? shopId = null, string? categoryId = null);
    Items? UpdateItems(Items item);
    long TotalItemsIds(string itemsIds);
    long TotalItems(string? partnerId = null, string? shopId = null, string? categoryId = null);

    //=== Variant
    Variant CreateVariant(Variant item);
    Variant RestoreVariant(Variant item);
    Variant DeleteVariant(string variantId);
    Variant? FindByVariantId(string variantId);
    List<Variant> FindByVariantIds(string variantIds);
    PagingResult<Variant> ListVariant(Paging paging, string? itemsId = null);
    Variant? UpdateVariant(Variant item);
    long TotalVariant(string itemsId);

    //=== Items-Variant
    PagingResult<object> ListQueryLookupVariant(Paging paging);

    //=== Function
    ItemsGroupBy? GroupByVariant(string itemsCode);
    PagingResult<ItemsGroupBy> ListQueryGroupByVariant(Paging paging, string? shopId = null, string? categoryId = null, string? subCategoryId = null, TypeItems? itemsType = null);
    PagingResult<ItemsGroupBy> ListQueryGroupByVariantExtend(bool isUser, Paging paging, ItemFilterDto model);

    //=== Order
    void UpdatePurchasedNumber(List<ItemsOrder> listItemsOrder, TypeOrderStatus statusOrder = TypeOrderStatus.Success);

    //=== Excel
    Task<byte[]> ExportItemsTemplate(string shopId, TypeItems type);

    Task<byte[]> ExportItemsToExcel(List<ItemsGroupByDto> items, TypeItems type, string shopId);

    Task<Items> UpdateImages(string shopId, string itemsCode, List<MediaInfo> images);
}