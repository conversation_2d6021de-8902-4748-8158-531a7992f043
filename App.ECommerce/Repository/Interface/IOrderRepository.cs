using App.ECommerce.ProcessFlow;
using App.Base.Resource.Model;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;
using MongoDB.Driver;
using static App.ECommerce.Controllers.API.OrderPartnerController;
using App.ECommerce.Units.Enums.Order;
using App.ECommerce.Units.Enums;

namespace App.ECommerce.Repository.Interface;

public interface IOrderRepository
{
    Task<Order> CreateOrder(Order item);
    Order RestoreOrder(Order item);
    Order DeleteOrder(string orderId);
    bool DeleteSameTransactionId(string transactionId, string userId);
    Order? FindByOrderId(string orderId);
    Order? FindByOrderNo(string orderNo);
    Order? FindByExternalId(string externalId, SyncServiceEnum externalSource);
    Order? FindByOrderThirdParty(string orderId);
    Order? FindByTransportOrderId(string transportOrderId, TypeTransportService typeTransportService);
    List<Order> FindByOrderIds(List<string> orderIds);
    List<Order> FindListByPartnerId(string partnerId);

    PagingResult<Order> ListOrder(Paging paging, string? partnerId = null, string? shopId = null, TypeStatus? status = null);
    Task<PagingResult<Order>> ListOrderExtend(Paging paging, ListOrderInputDto model);
    PagingResult<Order> ListOrderUser(Paging paging, string userId, TypeItems? typeItems, ListOrderStatusEnum statusOrder = ListOrderStatusEnum.All);
    List<Order> FindAll(TypeStatus? status = null);
    Order? UpdateOrder(Order item);
    long TotalOrder();
    long TotalOrderFilter(string? shopId = null, TypeOrderStatus[]? statusOrder = null, DateTime? from = null, DateTime? to = null);

    // More
    List<Order> CreateListOrder(List<Order> orders);
    List<Order> FindListByTransactionId(string transactionId);
    bool UpdateStatusListOrder(List<Order> listOrder, TypeOrderStatus statusOrder);

    Task<long> TotalPriceOrderByUserId(string userId, TypePointCalculation typePointCalculation);

    Task<decimal> GetTotalSpentByUserId(string userId);

    Task<PagingResult<Order>> GetOrdersAsync(List<FilterDefinition<Order>> filters, PagingConfig pagingInfo);
    public Task<List<Order>> GetOrdersByFilter(FilterDefinition<Order> filter);
    public List<Order> GetUniqueSuccessOrdersByCreatorId(List<string> userIds);
    Task<PagingResult<Order>> ListOrderSuccess(Paging paging, string f0UserId, List<string> subUserIds, DateTime? start = null);
    Task<List<Order>> GetUnpaidOrdersOlderThanMinutes(int minutes);
    Task<byte[]> ExportListOrder(List<OrderPartnerDto> list);
    Task<TotalOrderStatusDto> GetTotalOrderStatus(string shopId, string userId);
    Task<TotalOrderStatusDto> GetTotalOrderStatusByFilter(ListOrderInputDto objFilter);
    int GetTotalQuantityPurchased(string shopId, string userId, string itemsId);
}