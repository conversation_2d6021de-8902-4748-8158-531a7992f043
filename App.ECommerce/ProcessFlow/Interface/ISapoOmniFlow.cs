using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.ProcessFlow.Interface
{
    public interface ISapoOmniFlow
    {
        Task<bool> SaveConfigAsync(SapoOmniConfigDto dto);
        Task<bool> UpdateConfigAsync(UpdateSapoOmniConfigDto dto);
        Task<bool> DeleteConfigAsync(string shopId, string domainApi);
        Task<SapoOmniConfigDto?> GetConfigByDomainApiAsync(string domainApi, string shopId);
        Task<SapoOmniConfigDto> GetConfigsByShopIdAsync(string shopId);

        Task<Result<bool>> AddOrUpdateProductFromSapoOmniWebhook(SapoOmniProductDto productDto, string domainApi, string accessToken);
        Task<Result<bool>> DeleteProductFromSapoOmniWebhook(long productId);
        Task<Result<bool>> AddOrUpdateOrderFromSapoOmniWebhook(SapoOmniOrderDto orderDto, string domainApi, string accessToken);
        Task<Result<bool>> DeleteOrderFromSapoOmniWebhook(long orderId, string domainApi);
        Task<Result<bool>> UpdateInventoryFromSapoOmniWebhook(SapoOmniInventoryWebhookDto inventoryDto, string domainApi, string accessToken);
        Task<Result<bool>> AddOrUpdateCustomerFromSapoOmniWebhook(SapoOmniCustomerDto customerDto, string domainApi, string accessToken);


        Task<bool> UpdateAccessTokenAsync(string domainApi, string shopId, string accessToken);
        Task<bool> UpdateConfigStatusAsync(string domainApi, string shopId, string status);

        Task<Result<string>> CreateOrderToSapoOmniAsync(Order order, string shopId);
        Task<Result<bool>> UpdateOrderToSapoOmniAsync(Order order, string shopId);
        Task<Result<bool>> CreateProductToSapoOmniAsync(Items product, string shopId);
        Task<Result<bool>> UpdateProductToSapoOmniAsync(Items product, string shopId);
        Task<Result<bool>> CreateCustomerToSapoOmniAsync(User customer, string shopId);
        Task<Result<bool>> UpdateCustomerToSapoOmniAsync(User customer, string shopId);

        Task<Result<string>> GetAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, string state = "", List<string>? requestedScopes = null);
        Task<Result<string>> GetMinimalAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, string state = "");
        Task<Result<string>> GetCustomAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, List<string> requiredScopes, string state = "");
        Task<Result<string>> ExchangeCodeForAccessTokenAsync(string shopId, string code, string clientId, string clientSecret);
        Task<Result<bool>> ValidateAccessTokenAsync(string shopId);
        Task<Result<bool>> CreateWebhooksAsync(string shopId, string webhookUrl);
        Task<Result<bool>> DeleteWebhooksAsync(string shopId);
        Task<Result<List<SapoOmniWebhookInfo>>> GetWebhooksAsync(string shopId);
       
    }
}